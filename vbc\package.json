{"name": "nuxt-app", "type": "module", "private": true, "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@nuxt/icon": "^2.0.0", "@nuxtjs/tailwindcss": "^6.14.0", "bcryptjs": "^3.0.2", "connect-flash": "^0.1.1", "connect-mongo": "^5.1.0", "dotenv": "^17.2.0", "express": "^5.1.0", "mongoose": "^8.16.3", "nuxt": "^4.0.3", "passport": "^0.7.0", "passport-local": "^1.0.0", "swiper": "^11.2.10", "vue": "^3.5.18", "vue-router": "^4.5.1"}}