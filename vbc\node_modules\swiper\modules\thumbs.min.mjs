import{g as getDocument}from"../shared/ssr-window.esm.min.mjs";import{o as isObject,e as elementChildren}from"../shared/utils.min.mjs";function Thumb(e){let{swiper:s,extendParams:i,on:t}=e;i({thumbs:{swiper:null,multipleActiveThumbs:!0,autoScrollOffset:0,slideThumbActiveClass:"swiper-slide-thumb-active",thumbsContainerClass:"swiper-thumbs"}});let r=!1,a=!1;function n(){const e=s.thumbs.swiper;if(!e||e.destroyed)return;const i=e.clickedIndex,t=e.clickedSlide;if(t&&t.classList.contains(s.params.thumbs.slideThumbActiveClass))return;if(null==i)return;let r;r=e.params.loop?parseInt(e.clickedSlide.getAttribute("data-swiper-slide-index"),10):i,s.params.loop?s.slideToLoop(r):s.slideTo(r)}function l(){const{thumbs:e}=s.params;if(r)return!1;r=!0;const i=s.constructor;if(e.swiper instanceof i){if(e.swiper.destroyed)return r=!1,!1;s.thumbs.swiper=e.swiper,Object.assign(s.thumbs.swiper.originalParams,{watchSlidesProgress:!0,slideToClickedSlide:!1}),Object.assign(s.thumbs.swiper.params,{watchSlidesProgress:!0,slideToClickedSlide:!1}),s.thumbs.swiper.update()}else if(isObject(e.swiper)){const t=Object.assign({},e.swiper);Object.assign(t,{watchSlidesProgress:!0,slideToClickedSlide:!1}),s.thumbs.swiper=new i(t),a=!0}return s.thumbs.swiper.el.classList.add(s.params.thumbs.thumbsContainerClass),s.thumbs.swiper.on("tap",n),!0}function d(e){const i=s.thumbs.swiper;if(!i||i.destroyed)return;const t="auto"===i.params.slidesPerView?i.slidesPerViewDynamic():i.params.slidesPerView;let r=1;const a=s.params.thumbs.slideThumbActiveClass;if(s.params.slidesPerView>1&&!s.params.centeredSlides&&(r=s.params.slidesPerView),s.params.thumbs.multipleActiveThumbs||(r=1),r=Math.floor(r),i.slides.forEach((e=>e.classList.remove(a))),i.params.loop||i.params.virtual&&i.params.virtual.enabled)for(let e=0;e<r;e+=1)elementChildren(i.slidesEl,`[data-swiper-slide-index="${s.realIndex+e}"]`).forEach((e=>{e.classList.add(a)}));else for(let e=0;e<r;e+=1)i.slides[s.realIndex+e]&&i.slides[s.realIndex+e].classList.add(a);const n=s.params.thumbs.autoScrollOffset,l=n&&!i.params.loop;if(s.realIndex!==i.realIndex||l){const r=i.activeIndex;let a,d;if(i.params.loop){const e=i.slides.find((e=>e.getAttribute("data-swiper-slide-index")===`${s.realIndex}`));a=i.slides.indexOf(e),d=s.activeIndex>s.previousIndex?"next":"prev"}else a=s.realIndex,d=a>s.previousIndex?"next":"prev";l&&(a+="next"===d?n:-1*n),i.visibleSlidesIndexes&&i.visibleSlidesIndexes.indexOf(a)<0&&(i.params.centeredSlides?a=a>r?a-Math.floor(t/2)+1:a+Math.floor(t/2)-1:a>r&&i.params.slidesPerGroup,i.slideTo(a,e?0:void 0))}}s.thumbs={swiper:null},t("beforeInit",(()=>{const{thumbs:e}=s.params;if(e&&e.swiper)if("string"==typeof e.swiper||e.swiper instanceof HTMLElement){const i=getDocument(),t=()=>{const t="string"==typeof e.swiper?i.querySelector(e.swiper):e.swiper;if(t&&t.swiper)e.swiper=t.swiper,l(),d(!0);else if(t){const i=`${s.params.eventsPrefix}init`,r=a=>{e.swiper=a.detail[0],t.removeEventListener(i,r),l(),d(!0),e.swiper.update(),s.update()};t.addEventListener(i,r)}return t},r=()=>{if(s.destroyed)return;t()||requestAnimationFrame(r)};requestAnimationFrame(r)}else l(),d(!0)})),t("slideChange update resize observerUpdate",(()=>{d()})),t("setTransition",((e,i)=>{const t=s.thumbs.swiper;t&&!t.destroyed&&t.setTransition(i)})),t("beforeDestroy",(()=>{const e=s.thumbs.swiper;e&&!e.destroyed&&a&&e.destroy()})),Object.assign(s.thumbs,{init:l,update:d})}export{Thumb as default};
//# sourceMappingURL=thumbs.min.mjs.map