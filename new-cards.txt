    <!-- Game Modes Section -->
    <section class="bg-gray-900 p-6 md:p-8">
      <div class="max-w-7xl mx-auto">
        <h2 class="text-3xl font-bold text-white mb-6 pb-2 border-b border-gray-700 flex items-center">
          <Icon name="heroicons:puzzle-piece" class="mr-3 text-yellow-500" />
          Game Modes
        </h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <GameModeCard
            title="Career Mode"
            description="Build your fighter from rookie to champion"
            icon="heroicons:user-circle"
            color="blue"
            button-text="Start Career"
            @click="handleGameModeClick('career')"
          />
          <GameModeCard
            title="Tournaments"
            description="Compete in structured championship events"
            icon="heroicons:trophy"
            color="red"
            button-text="Join Tournament"
            @click="handleGameModeClick('tournament')"
          />
          <GameModeCard
            title="Multiplayer"
            description="Challenge players from around the world"
            icon="heroicons:users"
            color="green"
            button-text="Find Match"
            @click="handleGameModeClick('multiplayer')"
          />
          <GameModeCard
            title="Custom Fight"
            description="Create your own fight rules and scenarios"
            icon="heroicons:hand-raised"
            color="purple"
            button-text="Create Fight"
            @click="handleGameModeClick('custom')"
          />
          <GameModeCard
            title="Training"
            description="Practice combos and perfect your technique"
            icon="heroicons:academic-cap"
            color="orange"
            button-text="Start Training"
            @click="handleGameModeClick('training')"
          />
          <GameModeCard
            title="Fighter Roster"
            description="Browse and unlock all available fighters"
            icon="heroicons:book-open"
            color="teal"
            button-text="View Roster"
            @click="handleGameModeClick('roster')"
          />
        </div>
      </div>
    </section>

    <template>
  <div :class="cardClasses">
    <Icon :name="icon" class="text-4xl mb-4 text-white" />
    <h3 class="text-xl font-bold mb-2 text-white">{{ title }}</h3>
    <p :class="descriptionClasses">{{ description }}</p>
    <button :class="buttonClasses" @click="handleClick">
      {{ buttonText }}
    </button>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  title: {
    type: String,
    required: true
  },
  description: {
    type: String,
    required: true
  },
  icon: {
    type: String,
    required: true
  },
  color: {
    type: String,
    default: 'blue'
  },
  buttonText: {
    type: String,
    required: true
  }
})

const emit = defineEmits(['click'])

const handleClick = () => {
  emit('click')
}

const cardClasses = computed(() => {
  const colorMap = {
    'blue': 'bg-gradient-to-br from-blue-600 to-blue-800',
    'red': 'bg-gradient-to-br from-red-600 to-red-800',
    'green': 'bg-gradient-to-br from-green-600 to-green-800',
    'purple': 'bg-gradient-to-br from-purple-600 to-purple-800',
    'orange': 'bg-gradient-to-br from-orange-600 to-orange-800',
    'teal': 'bg-gradient-to-br from-teal-600 to-teal-800'
  }
  return `${colorMap[props.color] || colorMap.blue} rounded-xl p-6 text-center card-hover`
})



const descriptionClasses = computed(() => {
  const colorMap = {
    'blue': 'text-blue-100',
    'red': 'text-red-100',
    'green': 'text-green-100',
    'purple': 'text-purple-100',
    'orange': 'text-orange-100',
    'teal': 'text-teal-100'
  }
  return `${colorMap[props.color] || colorMap.blue} text-sm mb-4`
})

const buttonClasses = computed(() => {
  const colorMap = {
    'blue': 'w-full bg-white text-blue-800 font-semibold py-2 rounded-lg hover:bg-blue-50 transition-colors',
    'red': 'w-full bg-white text-red-800 font-semibold py-2 rounded-lg hover:bg-red-50 transition-colors',
    'green': 'w-full bg-white text-green-800 font-semibold py-2 rounded-lg hover:bg-green-50 transition-colors',
    'purple': 'w-full bg-white text-purple-800 font-semibold py-2 rounded-lg hover:bg-purple-50 transition-colors',
    'orange': 'w-full bg-white text-orange-800 font-semibold py-2 rounded-lg hover:bg-orange-50 transition-colors',
    'teal': 'w-full bg-white text-teal-800 font-semibold py-2 rounded-lg hover:bg-teal-50 transition-colors'
  }
  return colorMap[props.color] || colorMap.blue
})
</script>

<script setup lang="ts">
import { ref } from 'vue';

const gameModes = ref([
  {
    icon: 'uil:user-circle',
    title: 'Legacy Mode',
    description: 'Forge your legend from amateur to champion.'
  },
  {
    icon: 'uil:trophy',
    title: 'Tournaments',
    description: 'Compete for glory in structured events.'
  },
  {
    icon: 'iconoir:boxing-glove',
    title: 'Custom Fight',
    description: 'Set your own rules for ultimate control.'
  },
  {
    icon: 'uil:users-alt',
    title: 'Multiplayer Arena',
    description: 'Challenge players worldwide.'
  },
  {
    icon: 'uil:user-plus',
    title: 'Friend Match',
    description: 'Fight your friends head-to-head.'
  },
  {
    icon: 'uil:book-open',
    title: 'Fighter Roster',
    description: 'Browse all available fighters.'
  }
]);
</script>

<template>
  <div class="game-modes-container">
    <h1 class="section-title">Game Modes</h1>
    <div class="game-modes-grid">
      <GameModeCard
        v-for="(mode, index) in gameModes"
        :key="index"
        :icon="mode.icon"
        :title="mode.title"
        :description="mode.description"
      />
    </div>
  </div>
</template>

<style scoped>
.game-modes-container {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.section-title {
  color: white;
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 2rem;
  text-align: left;
}

.game-modes-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24px;
}

@media (max-width: 1024px) {
  .game-modes-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 640px) {
  .game-modes-grid {
    grid-template-columns: 1fr;
  }
}
</style>
