import{c as classesToSelector}from"../shared/classes-to-selector.min.mjs";import{c as createElementIfNotDefined}from"../shared/create-element-if-not-defined.min.mjs";import{m as makeElementsArray,h as elementOuterSize,i as elementIndex,s as setInnerHTML,b as elementParents}from"../shared/utils.min.mjs";function Pagination(e){let{swiper:a,extendParams:s,on:l,emit:t}=e;const i="swiper-pagination";let n;s({pagination:{el:null,bulletElement:"span",clickable:!1,hideOnClick:!1,renderBullet:null,renderProgressbar:null,renderFraction:null,renderCustom:null,progressbarOpposite:!1,type:"bullets",dynamicBullets:!1,dynamicMainBullets:1,formatFractionCurrent:e=>e,formatFractionTotal:e=>e,bulletClass:`${i}-bullet`,bulletActiveClass:`${i}-bullet-active`,modifierClass:`${i}-`,currentClass:`${i}-current`,totalClass:`${i}-total`,hiddenClass:`${i}-hidden`,progressbarFillClass:`${i}-progressbar-fill`,progressbarOppositeClass:`${i}-progressbar-opposite`,clickableClass:`${i}-clickable`,lockClass:`${i}-lock`,horizontalClass:`${i}-horizontal`,verticalClass:`${i}-vertical`,paginationDisabledClass:`${i}-disabled`}}),a.pagination={el:null,bullets:[]};let r=0;function o(){return!a.params.pagination.el||!a.pagination.el||Array.isArray(a.pagination.el)&&0===a.pagination.el.length}function p(e,s){const{bulletActiveClass:l}=a.params.pagination;e&&(e=e[("prev"===s?"previous":"next")+"ElementSibling"])&&(e.classList.add(`${l}-${s}`),(e=e[("prev"===s?"previous":"next")+"ElementSibling"])&&e.classList.add(`${l}-${s}-${s}`))}function c(e){const s=e.target.closest(classesToSelector(a.params.pagination.bulletClass));if(!s)return;e.preventDefault();const l=elementIndex(s)*a.params.slidesPerGroup;if(a.params.loop){if(a.realIndex===l)return;const e=(t=a.realIndex,i=l,n=a.slides.length,(i%=n)==1+(t%=n)?"next":i===t-1?"previous":void 0);"next"===e?a.slideNext():"previous"===e?a.slidePrev():a.slideToLoop(l)}else a.slideTo(l);var t,i,n}function d(){const e=a.rtl,s=a.params.pagination;if(o())return;let l,i,c=a.pagination.el;c=makeElementsArray(c);const d=a.virtual&&a.params.virtual.enabled?a.virtual.slides.length:a.slides.length,m=a.params.loop?Math.ceil(d/a.params.slidesPerGroup):a.snapGrid.length;if(a.params.loop?(i=a.previousRealIndex||0,l=a.params.slidesPerGroup>1?Math.floor(a.realIndex/a.params.slidesPerGroup):a.realIndex):void 0!==a.snapIndex?(l=a.snapIndex,i=a.previousSnapIndex):(i=a.previousIndex||0,l=a.activeIndex||0),"bullets"===s.type&&a.pagination.bullets&&a.pagination.bullets.length>0){const t=a.pagination.bullets;let o,d,m;if(s.dynamicBullets&&(n=elementOuterSize(t[0],a.isHorizontal()?"width":"height",!0),c.forEach((e=>{e.style[a.isHorizontal()?"width":"height"]=n*(s.dynamicMainBullets+4)+"px"})),s.dynamicMainBullets>1&&void 0!==i&&(r+=l-(i||0),r>s.dynamicMainBullets-1?r=s.dynamicMainBullets-1:r<0&&(r=0)),o=Math.max(l-r,0),d=o+(Math.min(t.length,s.dynamicMainBullets)-1),m=(d+o)/2),t.forEach((e=>{const a=[...["","-next","-next-next","-prev","-prev-prev","-main"].map((e=>`${s.bulletActiveClass}${e}`))].map((e=>"string"==typeof e&&e.includes(" ")?e.split(" "):e)).flat();e.classList.remove(...a)})),c.length>1)t.forEach((e=>{const t=elementIndex(e);t===l?e.classList.add(...s.bulletActiveClass.split(" ")):a.isElement&&e.setAttribute("part","bullet"),s.dynamicBullets&&(t>=o&&t<=d&&e.classList.add(...`${s.bulletActiveClass}-main`.split(" ")),t===o&&p(e,"prev"),t===d&&p(e,"next"))}));else{const e=t[l];if(e&&e.classList.add(...s.bulletActiveClass.split(" ")),a.isElement&&t.forEach(((e,a)=>{e.setAttribute("part",a===l?"bullet-active":"bullet")})),s.dynamicBullets){const e=t[o],a=t[d];for(let e=o;e<=d;e+=1)t[e]&&t[e].classList.add(...`${s.bulletActiveClass}-main`.split(" "));p(e,"prev"),p(a,"next")}}if(s.dynamicBullets){const l=Math.min(t.length,s.dynamicMainBullets+4),i=(n*l-n)/2-m*n,r=e?"right":"left";t.forEach((e=>{e.style[a.isHorizontal()?r:"top"]=`${i}px`}))}}c.forEach(((e,i)=>{if("fraction"===s.type&&(e.querySelectorAll(classesToSelector(s.currentClass)).forEach((e=>{e.textContent=s.formatFractionCurrent(l+1)})),e.querySelectorAll(classesToSelector(s.totalClass)).forEach((e=>{e.textContent=s.formatFractionTotal(m)}))),"progressbar"===s.type){let t;t=s.progressbarOpposite?a.isHorizontal()?"vertical":"horizontal":a.isHorizontal()?"horizontal":"vertical";const i=(l+1)/m;let n=1,r=1;"horizontal"===t?n=i:r=i,e.querySelectorAll(classesToSelector(s.progressbarFillClass)).forEach((e=>{e.style.transform=`translate3d(0,0,0) scaleX(${n}) scaleY(${r})`,e.style.transitionDuration=`${a.params.speed}ms`}))}"custom"===s.type&&s.renderCustom?(setInnerHTML(e,s.renderCustom(a,l+1,m)),0===i&&t("paginationRender",e)):(0===i&&t("paginationRender",e),t("paginationUpdate",e)),a.params.watchOverflow&&a.enabled&&e.classList[a.isLocked?"add":"remove"](s.lockClass)}))}function m(){const e=a.params.pagination;if(o())return;const s=a.virtual&&a.params.virtual.enabled?a.virtual.slides.length:a.grid&&a.params.grid.rows>1?a.slides.length/Math.ceil(a.params.grid.rows):a.slides.length;let l=a.pagination.el;l=makeElementsArray(l);let i="";if("bullets"===e.type){let l=a.params.loop?Math.ceil(s/a.params.slidesPerGroup):a.snapGrid.length;a.params.freeMode&&a.params.freeMode.enabled&&l>s&&(l=s);for(let s=0;s<l;s+=1)e.renderBullet?i+=e.renderBullet.call(a,s,e.bulletClass):i+=`<${e.bulletElement} ${a.isElement?'part="bullet"':""} class="${e.bulletClass}"></${e.bulletElement}>`}"fraction"===e.type&&(i=e.renderFraction?e.renderFraction.call(a,e.currentClass,e.totalClass):`<span class="${e.currentClass}"></span> / <span class="${e.totalClass}"></span>`),"progressbar"===e.type&&(i=e.renderProgressbar?e.renderProgressbar.call(a,e.progressbarFillClass):`<span class="${e.progressbarFillClass}"></span>`),a.pagination.bullets=[],l.forEach((s=>{"custom"!==e.type&&setInnerHTML(s,i||""),"bullets"===e.type&&a.pagination.bullets.push(...s.querySelectorAll(classesToSelector(e.bulletClass)))})),"custom"!==e.type&&t("paginationRender",l[0])}function u(){a.params.pagination=createElementIfNotDefined(a,a.originalParams.pagination,a.params.pagination,{el:"swiper-pagination"});const e=a.params.pagination;if(!e.el)return;let s;"string"==typeof e.el&&a.isElement&&(s=a.el.querySelector(e.el)),s||"string"!=typeof e.el||(s=[...document.querySelectorAll(e.el)]),s||(s=e.el),s&&0!==s.length&&(a.params.uniqueNavElements&&"string"==typeof e.el&&Array.isArray(s)&&s.length>1&&(s=[...a.el.querySelectorAll(e.el)],s.length>1&&(s=s.find((e=>elementParents(e,".swiper")[0]===a.el)))),Array.isArray(s)&&1===s.length&&(s=s[0]),Object.assign(a.pagination,{el:s}),s=makeElementsArray(s),s.forEach((s=>{"bullets"===e.type&&e.clickable&&s.classList.add(...(e.clickableClass||"").split(" ")),s.classList.add(e.modifierClass+e.type),s.classList.add(a.isHorizontal()?e.horizontalClass:e.verticalClass),"bullets"===e.type&&e.dynamicBullets&&(s.classList.add(`${e.modifierClass}${e.type}-dynamic`),r=0,e.dynamicMainBullets<1&&(e.dynamicMainBullets=1)),"progressbar"===e.type&&e.progressbarOpposite&&s.classList.add(e.progressbarOppositeClass),e.clickable&&s.addEventListener("click",c),a.enabled||s.classList.add(e.lockClass)})))}function g(){const e=a.params.pagination;if(o())return;let s=a.pagination.el;s&&(s=makeElementsArray(s),s.forEach((s=>{s.classList.remove(e.hiddenClass),s.classList.remove(e.modifierClass+e.type),s.classList.remove(a.isHorizontal()?e.horizontalClass:e.verticalClass),e.clickable&&(s.classList.remove(...(e.clickableClass||"").split(" ")),s.removeEventListener("click",c))}))),a.pagination.bullets&&a.pagination.bullets.forEach((a=>a.classList.remove(...e.bulletActiveClass.split(" "))))}l("changeDirection",(()=>{if(!a.pagination||!a.pagination.el)return;const e=a.params.pagination;let{el:s}=a.pagination;s=makeElementsArray(s),s.forEach((s=>{s.classList.remove(e.horizontalClass,e.verticalClass),s.classList.add(a.isHorizontal()?e.horizontalClass:e.verticalClass)}))})),l("init",(()=>{!1===a.params.pagination.enabled?b():(u(),m(),d())})),l("activeIndexChange",(()=>{void 0===a.snapIndex&&d()})),l("snapIndexChange",(()=>{d()})),l("snapGridLengthChange",(()=>{m(),d()})),l("destroy",(()=>{g()})),l("enable disable",(()=>{let{el:e}=a.pagination;e&&(e=makeElementsArray(e),e.forEach((e=>e.classList[a.enabled?"remove":"add"](a.params.pagination.lockClass))))})),l("lock unlock",(()=>{d()})),l("click",((e,s)=>{const l=s.target,i=makeElementsArray(a.pagination.el);if(a.params.pagination.el&&a.params.pagination.hideOnClick&&i&&i.length>0&&!l.classList.contains(a.params.pagination.bulletClass)){if(a.navigation&&(a.navigation.nextEl&&l===a.navigation.nextEl||a.navigation.prevEl&&l===a.navigation.prevEl))return;const e=i[0].classList.contains(a.params.pagination.hiddenClass);t(!0===e?"paginationShow":"paginationHide"),i.forEach((e=>e.classList.toggle(a.params.pagination.hiddenClass)))}}));const b=()=>{a.el.classList.add(a.params.pagination.paginationDisabledClass);let{el:e}=a.pagination;e&&(e=makeElementsArray(e),e.forEach((e=>e.classList.add(a.params.pagination.paginationDisabledClass)))),g()};Object.assign(a.pagination,{enable:()=>{a.el.classList.remove(a.params.pagination.paginationDisabledClass);let{el:e}=a.pagination;e&&(e=makeElementsArray(e),e.forEach((e=>e.classList.remove(a.params.pagination.paginationDisabledClass)))),u(),m(),d()},disable:b,render:m,update:d,init:u,destroy:g})}export{Pagination as default};
//# sourceMappingURL=pagination.min.mjs.map