// generated by the @nuxtjs/tailwindcss <https://github.com/nuxt-modules/tailwindcss> module at 9/4/2025, 1:26:34 PM
import "@nuxtjs/tailwindcss/config-ctx"
import configMerger from "@nuxtjs/tailwindcss/merger";

;
const config = [
{"content":{"files":["C:/Users/<USER>/Documents/augment-projects/nuxt-vbc/vbc/components/**/*.{vue,js,jsx,mjs,ts,tsx}","C:/Users/<USER>/Documents/augment-projects/nuxt-vbc/vbc/components/global/**/*.{vue,js,jsx,mjs,ts,tsx}","C:/Users/<USER>/Documents/augment-projects/nuxt-vbc/vbc/components/**/*.{vue,js,jsx,mjs,ts,tsx}","C:/Users/<USER>/Documents/augment-projects/nuxt-vbc/vbc/layouts/**/*.{vue,js,jsx,mjs,ts,tsx}","C:/Users/<USER>/Documents/augment-projects/nuxt-vbc/vbc/plugins/**/*.{js,ts,mjs}","C:/Users/<USER>/Documents/augment-projects/nuxt-vbc/vbc/composables/**/*.{js,ts,mjs}","C:/Users/<USER>/Documents/augment-projects/nuxt-vbc/vbc/utils/**/*.{js,ts,mjs}","C:/Users/<USER>/Documents/augment-projects/nuxt-vbc/vbc/pages/**/*.{vue,js,jsx,mjs,ts,tsx}","C:/Users/<USER>/Documents/augment-projects/nuxt-vbc/vbc/{A,a}pp.{vue,js,jsx,mjs,ts,tsx}","C:/Users/<USER>/Documents/augment-projects/nuxt-vbc/vbc/{E,e}rror.{vue,js,jsx,mjs,ts,tsx}","C:/Users/<USER>/Documents/augment-projects/nuxt-vbc/vbc/app.config.{js,ts,mjs}"]}},
{}
].reduce((acc, curr) => configMerger(acc, curr), {});

const resolvedConfig = config;

export default resolvedConfig;